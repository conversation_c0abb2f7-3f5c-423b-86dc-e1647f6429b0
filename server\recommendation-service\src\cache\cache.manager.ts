/**
 * 缓存管理器
 * 提供推荐结果的缓存功能
 */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
import { CacheConfig } from '../interfaces/recommendation.interface';

@Injectable()
export class CacheManager {
  private readonly logger = new Logger(CacheManager.name);
  private redis: Redis;
  private config: CacheConfig;

  constructor(private configService: ConfigService) {
    this.config = {
      ttl: this.configService.get<number>('CACHE_TTL', 300), // 5分钟默认TTL
      maxSize: this.configService.get<number>('CACHE_MAX_SIZE', 10000),
      strategy: 'lru'
    };

    this.initializeRedis();
  }

  /**
   * 初始化Redis连接
   */
  private initializeRedis(): void {
    try {
      this.redis = new Redis({
        host: this.configService.get<string>('REDIS_HOST', 'localhost'),
        port: this.configService.get<number>('REDIS_PORT', 6379),
        password: this.configService.get<string>('REDIS_PASSWORD'),
        db: this.configService.get<number>('REDIS_DB', 0),
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        keyPrefix: 'rec:',
      });

      this.redis.on('connect', () => {
        this.logger.log('Redis连接已建立');
      });

      this.redis.on('error', (error) => {
        this.logger.error('Redis连接错误:', error);
      });

    } catch (error) {
      this.logger.error('Redis初始化失败:', error);
    }
  }

  /**
   * 获取缓存值
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(key);
      if (value) {
        return JSON.parse(value) as T;
      }
      return null;
    } catch (error) {
      this.logger.error(`缓存获取失败 [${key}]:`, error);
      return null;
    }
  }

  /**
   * 设置缓存值
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      const cacheTtl = ttl || this.config.ttl;
      
      await this.redis.setex(key, cacheTtl, serializedValue);
      this.logger.debug(`缓存已设置 [${key}], TTL: ${cacheTtl}s`);
    } catch (error) {
      this.logger.error(`缓存设置失败 [${key}]:`, error);
    }
  }

  /**
   * 删除缓存
   */
  async delete(key: string): Promise<void> {
    try {
      await this.redis.del(key);
      this.logger.debug(`缓存已删除 [${key}]`);
    } catch (error) {
      this.logger.error(`缓存删除失败 [${key}]:`, error);
    }
  }

  /**
   * 批量删除缓存
   */
  async deletePattern(pattern: string): Promise<void> {
    try {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(...keys);
        this.logger.debug(`批量删除缓存 [${pattern}]: ${keys.length}个键`);
      }
    } catch (error) {
      this.logger.error(`批量删除缓存失败 [${pattern}]:`, error);
    }
  }

  /**
   * 检查缓存是否存在
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.redis.exists(key);
      return result === 1;
    } catch (error) {
      this.logger.error(`缓存检查失败 [${key}]:`, error);
      return false;
    }
  }

  /**
   * 获取缓存TTL
   */
  async getTtl(key: string): Promise<number> {
    try {
      return await this.redis.ttl(key);
    } catch (error) {
      this.logger.error(`获取TTL失败 [${key}]:`, error);
      return -1;
    }
  }

  /**
   * 设置缓存过期时间
   */
  async expire(key: string, ttl: number): Promise<void> {
    try {
      await this.redis.expire(key, ttl);
      this.logger.debug(`设置过期时间 [${key}]: ${ttl}s`);
    } catch (error) {
      this.logger.error(`设置过期时间失败 [${key}]:`, error);
    }
  }

  /**
   * 增量操作
   */
  async increment(key: string, value: number = 1): Promise<number> {
    try {
      return await this.redis.incrby(key, value);
    } catch (error) {
      this.logger.error(`增量操作失败 [${key}]:`, error);
      return 0;
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getStats(): Promise<Record<string, any>> {
    try {
      const info = await this.redis.info('memory');
      const keyspace = await this.redis.info('keyspace');
      
      return {
        memory: this.parseRedisInfo(info),
        keyspace: this.parseRedisInfo(keyspace),
        config: this.config
      };
    } catch (error) {
      this.logger.error('获取缓存统计失败:', error);
      return {};
    }
  }

  /**
   * 解析Redis信息
   */
  private parseRedisInfo(info: string): Record<string, any> {
    const result: Record<string, any> = {};
    const lines = info.split('\r\n');
    
    for (const line of lines) {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        result[key] = isNaN(Number(value)) ? value : Number(value);
      }
    }
    
    return result;
  }

  /**
   * 清空所有缓存
   */
  async flush(): Promise<void> {
    try {
      await this.redis.flushdb();
      this.logger.log('缓存已清空');
    } catch (error) {
      this.logger.error('清空缓存失败:', error);
    }
  }

  /**
   * 关闭连接
   */
  async close(): Promise<void> {
    try {
      await this.redis.quit();
      this.logger.log('Redis连接已关闭');
    } catch (error) {
      this.logger.error('关闭Redis连接失败:', error);
    }
  }
}
