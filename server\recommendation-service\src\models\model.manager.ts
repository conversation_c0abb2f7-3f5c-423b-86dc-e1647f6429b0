/**
 * 模型管理器
 * 负责机器学习模型的加载、训练和管理
 */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as tf from '@tensorflow/tfjs-node';
import { 
  ModelConfig, 
  TrainingData, 
  RecommendationMetrics,
  RecommendationFeedback 
} from '../interfaces/recommendation.interface';

@Injectable()
export class ModelManager {
  private readonly logger = new Logger(ModelManager.name);
  private models: Map<string, tf.LayersModel> = new Map();
  private modelConfigs: Map<string, ModelConfig> = new Map();
  private isTraining: Map<string, boolean> = new Map();

  constructor(private configService: ConfigService) {
    this.initializeModels();
  }

  /**
   * 初始化模型
   */
  private async initializeModels(): Promise<void> {
    try {
      // 加载预训练模型
      await this.loadPretrainedModels();
      this.logger.log('模型管理器初始化完成');
    } catch (error) {
      this.logger.error('模型初始化失败:', error);
    }
  }

  /**
   * 加载预训练模型
   */
  private async loadPretrainedModels(): Promise<void> {
    const modelPaths = {
      'collaborative_filtering': this.configService.get<string>('CF_MODEL_PATH'),
      'content_based': this.configService.get<string>('CB_MODEL_PATH'),
      'hybrid': this.configService.get<string>('HYBRID_MODEL_PATH')
    };

    for (const [modelName, modelPath] of Object.entries(modelPaths)) {
      if (modelPath) {
        try {
          const model = await tf.loadLayersModel(`file://${modelPath}`);
          this.models.set(modelName, model);
          this.logger.log(`模型加载成功: ${modelName}`);
        } catch (error) {
          this.logger.warn(`模型加载失败 [${modelName}]:`, error.message);
        }
      }
    }
  }

  /**
   * 获取模型
   */
  getModel(modelName: string): tf.LayersModel | null {
    return this.models.get(modelName) || null;
  }

  /**
   * 注册模型
   */
  registerModel(modelName: string, model: tf.LayersModel, config: ModelConfig): void {
    this.models.set(modelName, model);
    this.modelConfigs.set(modelName, config);
    this.logger.log(`模型已注册: ${modelName}`);
  }

  /**
   * 训练模型
   */
  async trainModel(
    modelName: string, 
    trainingData: TrainingData,
    config?: Partial<ModelConfig>
  ): Promise<void> {
    if (this.isTraining.get(modelName)) {
      throw new Error(`模型 ${modelName} 正在训练中`);
    }

    this.isTraining.set(modelName, true);
    
    try {
      this.logger.log(`开始训练模型: ${modelName}`);
      
      const model = this.models.get(modelName);
      if (!model) {
        throw new Error(`模型 ${modelName} 不存在`);
      }

      const modelConfig = { ...this.modelConfigs.get(modelName), ...config };
      
      // 准备训练数据
      const { inputs, outputs } = this.prepareTrainingData(trainingData);
      
      // 编译模型
      model.compile({
        optimizer: tf.train.adam(modelConfig.learningRate || 0.001),
        loss: 'meanSquaredError',
        metrics: ['mae', 'mse']
      });

      // 训练模型
      const history = await model.fit(inputs, outputs, {
        epochs: modelConfig.epochs || 100,
        batchSize: modelConfig.batchSize || 32,
        validationSplit: modelConfig.validationSplit || 0.2,
        shuffle: true,
        callbacks: {
          onEpochEnd: (epoch, logs) => {
            if (epoch % 10 === 0) {
              this.logger.log(
                `模型 ${modelName} - Epoch ${epoch}: ` +
                `loss=${logs?.loss?.toFixed(4)}, val_loss=${logs?.val_loss?.toFixed(4)}`
              );
            }
          }
        }
      });

      // 保存模型
      await this.saveModel(modelName);
      
      this.logger.log(`模型训练完成: ${modelName}`);
      
    } catch (error) {
      this.logger.error(`模型训练失败 [${modelName}]:`, error);
      throw error;
    } finally {
      this.isTraining.set(modelName, false);
    }
  }

  /**
   * 准备训练数据
   */
  private prepareTrainingData(trainingData: TrainingData): {
    inputs: tf.Tensor,
    outputs: tf.Tensor
  } {
    const interactions = trainingData.interactions;
    
    // 构建用户-物品矩阵
    const userIds = [...new Set(interactions.map(i => i.userId))];
    const itemIds = [...new Set(interactions.map(i => i.itemId))];
    
    const userToIndex = new Map(userIds.map((id, index) => [id, index]));
    const itemToIndex = new Map(itemIds.map((id, index) => [id, index]));
    
    const inputData: number[][] = [];
    const outputData: number[] = [];
    
    for (const interaction of interactions) {
      const userIndex = userToIndex.get(interaction.userId);
      const itemIndex = itemToIndex.get(interaction.itemId);
      
      if (userIndex !== undefined && itemIndex !== undefined) {
        inputData.push([userIndex, itemIndex]);
        outputData.push(interaction.rating);
      }
    }
    
    return {
      inputs: tf.tensor2d(inputData),
      outputs: tf.tensor1d(outputData)
    };
  }

  /**
   * 预测
   */
  async predict(modelName: string, inputs: tf.Tensor): Promise<tf.Tensor> {
    const model = this.models.get(modelName);
    if (!model) {
      throw new Error(`模型 ${modelName} 不存在`);
    }

    return model.predict(inputs) as tf.Tensor;
  }

  /**
   * 批量预测
   */
  async batchPredict(
    modelName: string, 
    userIndices: number[], 
    itemIndices: number[]
  ): Promise<number[]> {
    if (userIndices.length !== itemIndices.length) {
      throw new Error('用户索引和物品索引长度不匹配');
    }

    const model = this.models.get(modelName);
    if (!model) {
      throw new Error(`模型 ${modelName} 不存在`);
    }

    const inputData = userIndices.map((userIndex, i) => [userIndex, itemIndices[i]]);
    const inputs = tf.tensor2d(inputData);
    
    try {
      const predictions = await this.predict(modelName, inputs);
      const scores = await predictions.data();
      
      // 清理张量
      inputs.dispose();
      predictions.dispose();
      
      return Array.from(scores);
    } catch (error) {
      inputs.dispose();
      throw error;
    }
  }

  /**
   * 评估模型
   */
  async evaluateModel(
    modelName: string, 
    testData: TrainingData
  ): Promise<RecommendationMetrics> {
    const model = this.models.get(modelName);
    if (!model) {
      throw new Error(`模型 ${modelName} 不存在`);
    }

    const { inputs, outputs } = this.prepareTrainingData(testData);
    
    try {
      const evaluation = await model.evaluate(inputs, outputs) as tf.Scalar[];
      const [loss, mae, mse] = await Promise.all(evaluation.map(tensor => tensor.data()));
      
      // 计算其他指标
      const predictions = await this.predict(modelName, inputs);
      const predictionData = await predictions.data();
      const actualData = await outputs.data();
      
      const metrics = this.calculateMetrics(
        Array.from(actualData), 
        Array.from(predictionData)
      );
      
      // 清理张量
      inputs.dispose();
      outputs.dispose();
      predictions.dispose();
      evaluation.forEach(tensor => tensor.dispose());
      
      return {
        ...metrics,
        precision: 0, // 需要根据具体业务逻辑计算
        recall: 0,
        f1Score: 0,
        ndcg: 0
      };
      
    } catch (error) {
      inputs.dispose();
      outputs.dispose();
      throw error;
    }
  }

  /**
   * 计算评估指标
   */
  private calculateMetrics(actual: number[], predicted: number[]): Partial<RecommendationMetrics> {
    const n = actual.length;
    
    // 计算多样性
    const diversity = this.calculateDiversity(predicted);
    
    // 计算新颖性
    const novelty = this.calculateNovelty(predicted);
    
    // 计算覆盖率
    const coverage = this.calculateCoverage(predicted);
    
    // 计算惊喜度
    const serendipity = this.calculateSerendipity(actual, predicted);
    
    return {
      diversity,
      novelty,
      coverage,
      serendipity
    };
  }

  /**
   * 计算多样性
   */
  private calculateDiversity(predictions: number[]): number {
    if (predictions.length <= 1) return 0;
    
    let totalDistance = 0;
    let count = 0;
    
    for (let i = 0; i < predictions.length; i++) {
      for (let j = i + 1; j < predictions.length; j++) {
        totalDistance += Math.abs(predictions[i] - predictions[j]);
        count++;
      }
    }
    
    return count > 0 ? totalDistance / count : 0;
  }

  /**
   * 计算新颖性
   */
  private calculateNovelty(predictions: number[]): number {
    // 简化的新颖性计算：基于预测值的标准差
    const mean = predictions.reduce((sum, val) => sum + val, 0) / predictions.length;
    const variance = predictions.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / predictions.length;
    return Math.sqrt(variance);
  }

  /**
   * 计算覆盖率
   */
  private calculateCoverage(predictions: number[]): number {
    const uniqueValues = new Set(predictions.map(p => Math.round(p * 10) / 10));
    return uniqueValues.size / predictions.length;
  }

  /**
   * 计算惊喜度
   */
  private calculateSerendipity(actual: number[], predicted: number[]): number {
    let serendipitySum = 0;
    
    for (let i = 0; i < actual.length; i++) {
      const surprise = Math.abs(actual[i] - predicted[i]);
      const relevance = actual[i] > 3 ? 1 : 0; // 假设评分>3为相关
      serendipitySum += surprise * relevance;
    }
    
    return serendipitySum / actual.length;
  }

  /**
   * 保存模型
   */
  async saveModel(modelName: string, path?: string): Promise<void> {
    const model = this.models.get(modelName);
    if (!model) {
      throw new Error(`模型 ${modelName} 不存在`);
    }

    const savePath = path || `./models/${modelName}`;
    await model.save(`file://${savePath}`);
    this.logger.log(`模型已保存: ${modelName} -> ${savePath}`);
  }

  /**
   * 更新模型（增量学习）
   */
  async updateModel(
    modelName: string, 
    feedback: RecommendationFeedback[]
  ): Promise<void> {
    // 实现增量学习逻辑
    this.logger.log(`更新模型 ${modelName}，反馈数量: ${feedback.length}`);
    
    // 这里可以实现在线学习算法
    // 例如：梯度下降的单步更新
  }

  /**
   * 获取模型信息
   */
  getModelInfo(modelName: string): any {
    const model = this.models.get(modelName);
    const config = this.modelConfigs.get(modelName);
    const isTraining = this.isTraining.get(modelName) || false;
    
    if (!model) {
      return null;
    }
    
    return {
      name: modelName,
      config,
      isTraining,
      inputShape: model.inputs.map(input => input.shape),
      outputShape: model.outputs.map(output => output.shape),
      trainableParams: model.countParams(),
      layers: model.layers.length
    };
  }

  /**
   * 列出所有模型
   */
  listModels(): string[] {
    return Array.from(this.models.keys());
  }

  /**
   * 删除模型
   */
  deleteModel(modelName: string): void {
    const model = this.models.get(modelName);
    if (model) {
      model.dispose();
      this.models.delete(modelName);
      this.modelConfigs.delete(modelName);
      this.isTraining.delete(modelName);
      this.logger.log(`模型已删除: ${modelName}`);
    }
  }
}
