{"name": "recommendation-service", "version": "1.0.0", "description": "智能推荐服务 - 提供多种推荐算法和个性化推荐功能", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "docker:build": "docker build -t recommendation-service .", "docker:run": "docker run -p 3070:3070 recommendation-service"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/microservices": "^10.0.0", "@nestjs/schedule": "^4.0.0", "@nestjs/event-emitter": "^2.0.0", "@nestjs/terminus": "^10.0.0", "@tensorflow/tfjs": "^4.10.0", "@tensorflow/tfjs-node": "^4.10.0", "typeorm": "^0.3.17", "mysql2": "^3.6.0", "ioredis": "^5.3.2", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "swagger-ui-express": "^5.0.0", "compression": "^1.7.4", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1", "lodash": "^4.17.21", "moment": "^2.29.4", "uuid": "^9.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@types/lodash": "^4.14.195", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "keywords": ["recommendation", "machine-learning", "collaborative-filtering", "personalization", "<PERSON><PERSON><PERSON>", "microservice"], "author": "DL Engine Team", "license": "MIT"}