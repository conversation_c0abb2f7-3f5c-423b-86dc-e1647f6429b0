/**
 * 推荐服务项目结构验证脚本
 * 验证项目是否包含所有必需的文件和配置
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始验证推荐服务项目结构...\n');

// 必需的文件和目录
const requiredStructure = {
  files: [
    // 根配置文件
    'package.json',
    'tsconfig.json',
    'nest-cli.json',
    '.env.example',
    'Dockerfile',
    'docker-compose.yml',
    'README.md',
    
    // 应用核心文件
    'src/main.ts',
    'src/app.module.ts',
    'src/app.controller.ts',
    'src/recommendation.service.ts',
    
    // 接口定义
    'src/interfaces/recommendation.interface.ts',
    
    // 实体定义
    'src/entities/recommendation-history.entity.ts',
    'src/entities/user-interaction.entity.ts',
    'src/entities/user-profile.entity.ts',
    
    // 控制器
    'src/controllers/recommendation.controller.ts',
    'src/controllers/analytics.controller.ts',
    'src/controllers/health.controller.ts',
    
    // 核心组件
    'src/cache/cache.manager.ts',
    'src/models/model.manager.ts',
    
    // 算法
    'src/algorithms/neural-collaborative-filtering.ts'
  ],
  
  directories: [
    'src',
    'src/interfaces',
    'src/entities',
    'src/controllers',
    'src/cache',
    'src/models',
    'src/algorithms',
    'scripts'
  ]
};

let allValid = true;
let validCount = 0;

// 检查文件
console.log('📄 检查必需文件:');
console.log('-'.repeat(50));

requiredStructure.files.forEach(filePath => {
  const fullPath = path.join(__dirname, '..', filePath);
  const exists = fs.existsSync(fullPath) && fs.statSync(fullPath).isFile();
  
  if (exists) {
    console.log(`✅ ${filePath}`);
    validCount++;
  } else {
    console.log(`❌ ${filePath} - 文件缺失`);
    allValid = false;
  }
});

// 检查目录
console.log('\n📂 检查必需目录:');
console.log('-'.repeat(50));

requiredStructure.directories.forEach(dirPath => {
  const fullPath = path.join(__dirname, '..', dirPath);
  const exists = fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory();
  
  if (exists) {
    console.log(`✅ ${dirPath}/`);
    validCount++;
  } else {
    console.log(`❌ ${dirPath}/ - 目录缺失`);
    allValid = false;
  }
});

// 检查package.json依赖
console.log('\n📦 检查依赖配置:');
console.log('-'.repeat(50));

try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
  
  const requiredDependencies = [
    '@nestjs/common',
    '@nestjs/core',
    '@nestjs/platform-express',
    '@nestjs/typeorm',
    '@nestjs/config',
    '@nestjs/swagger',
    '@nestjs/terminus',
    '@tensorflow/tfjs',
    '@tensorflow/tfjs-node',
    'typeorm',
    'mysql2',
    'ioredis',
    'class-validator',
    'class-transformer'
  ];
  
  let depsValid = true;
  requiredDependencies.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
    } else {
      console.log(`❌ ${dep} - 依赖缺失`);
      depsValid = false;
      allValid = false;
    }
  });
  
  if (depsValid) {
    console.log('✅ 所有必需依赖都已配置');
  }
  
} catch (error) {
  console.log('❌ package.json读取失败:', error.message);
  allValid = false;
}

// 检查TypeScript配置
console.log('\n🔧 检查TypeScript配置:');
console.log('-'.repeat(50));

try {
  const tsConfig = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'tsconfig.json'), 'utf8'));
  
  const requiredCompilerOptions = [
    'experimentalDecorators',
    'emitDecoratorMetadata',
    'target',
    'module',
    'outDir'
  ];
  
  let tsValid = true;
  requiredCompilerOptions.forEach(option => {
    if (tsConfig.compilerOptions && tsConfig.compilerOptions[option] !== undefined) {
      console.log(`✅ ${option}: ${tsConfig.compilerOptions[option]}`);
    } else {
      console.log(`❌ ${option} - 配置缺失`);
      tsValid = false;
      allValid = false;
    }
  });
  
  if (tsValid) {
    console.log('✅ TypeScript配置正确');
  }
  
} catch (error) {
  console.log('❌ tsconfig.json读取失败:', error.message);
  allValid = false;
}

// 检查Docker配置
console.log('\n🐳 检查Docker配置:');
console.log('-'.repeat(50));

const dockerFiles = ['Dockerfile', 'docker-compose.yml'];
dockerFiles.forEach(file => {
  const filePath = path.join(__dirname, '..', file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} 存在`);
  } else {
    console.log(`❌ ${file} 缺失`);
    allValid = false;
  }
});

// 检查环境配置
console.log('\n⚙️  检查环境配置:');
console.log('-'.repeat(50));

const envExample = path.join(__dirname, '..', '.env.example');
if (fs.existsSync(envExample)) {
  console.log('✅ .env.example 存在');
  
  try {
    const envContent = fs.readFileSync(envExample, 'utf8');
    const requiredEnvVars = [
      'NODE_ENV',
      'PORT',
      'DB_HOST',
      'DB_PORT',
      'REDIS_HOST',
      'REDIS_PORT'
    ];
    
    let envValid = true;
    requiredEnvVars.forEach(envVar => {
      if (envContent.includes(envVar)) {
        console.log(`✅ ${envVar} 已配置`);
      } else {
        console.log(`❌ ${envVar} 环境变量缺失`);
        envValid = false;
        allValid = false;
      }
    });
    
    if (envValid) {
      console.log('✅ 环境变量配置完整');
    }
    
  } catch (error) {
    console.log('❌ .env.example读取失败:', error.message);
    allValid = false;
  }
} else {
  console.log('❌ .env.example 缺失');
  allValid = false;
}

// 最终结果
console.log('\n' + '='.repeat(60));
console.log('📊 验证结果统计:');
console.log('='.repeat(60));

const totalItems = requiredStructure.files.length + requiredStructure.directories.length;
console.log(`📁 文件/目录检查: ${validCount}/${totalItems} 通过`);

if (allValid) {
  console.log('\n🎉 项目结构验证通过！');
  console.log('✅ 推荐服务项目结构完整');
  console.log('✅ 所有必需文件都存在');
  console.log('✅ 依赖配置正确');
  console.log('✅ TypeScript配置正确');
  console.log('✅ Docker配置完整');
  console.log('✅ 环境配置完整');
  
  console.log('\n🚀 可以开始开发和部署！');
  console.log('\n📖 下一步操作:');
  console.log('   1. 复制 .env.example 为 .env 并配置环境变量');
  console.log('   2. 运行 npm install 安装依赖');
  console.log('   3. 启动数据库和Redis服务');
  console.log('   4. 运行 npm run start:dev 启动开发服务器');
  
  process.exit(0);
} else {
  console.log('\n❌ 项目结构验证失败！');
  console.log('请检查上述缺失的文件和配置');
  process.exit(1);
}
