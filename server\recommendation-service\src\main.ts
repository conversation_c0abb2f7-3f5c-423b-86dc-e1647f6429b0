/**
 * 智能推荐服务启动文件
 */
import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import * as compression from 'compression';
import * as helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  try {
    // 创建应用实例
    const app = await NestFactory.create(AppModule, {
      logger: ['log', 'error', 'warn', 'debug', 'verbose'],
    });

    const configService = app.get(ConfigService);

    // 全局中间件
    app.use(helmet());
    app.use(compression());

    // 速率限制
    app.use(
      rateLimit({
        windowMs: 15 * 60 * 1000, // 15分钟
        max: 1000, // 限制每个IP 15分钟内最多1000个请求
        message: '请求过于频繁，请稍后再试',
      }),
    );

    // 全局验证管道
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
        disableErrorMessages: false,
      }),
    );

    // CORS配置
    app.enableCors({
      origin: configService.get<string>('CORS_ORIGIN', '*'),
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    });

    // 全局前缀
    const globalPrefix = configService.get<string>('GLOBAL_PREFIX', 'api/v1');
    app.setGlobalPrefix(globalPrefix);

    // Swagger文档配置
    if (configService.get<string>('NODE_ENV') !== 'production') {
      const config = new DocumentBuilder()
        .setTitle('智能推荐服务 API')
        .setDescription('提供多种推荐算法和个性化推荐功能的微服务')
        .setVersion('1.0.0')
        .addTag('recommendations', '推荐相关接口')
        .addTag('algorithms', '算法管理接口')
        .addTag('feedback', '反馈处理接口')
        .addTag('analytics', '分析统计接口')
        .addTag('health', '健康检查接口')
        .addBearerAuth(
          {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            name: 'JWT',
            description: 'Enter JWT token',
            in: 'header',
          },
          'JWT-auth',
        )
        .addServer(`http://localhost:${configService.get<number>('PORT', 3070)}`, '开发环境')
        .addServer(`https://api.example.com`, '生产环境')
        .build();

      const document = SwaggerModule.createDocument(app, config);
      const swaggerPath = configService.get<string>('SWAGGER_PATH', 'docs');
      SwaggerModule.setup(swaggerPath, app, document, {
        swaggerOptions: {
          persistAuthorization: true,
          tagsSorter: 'alpha',
          operationsSorter: 'alpha',
        },
        customSiteTitle: '智能推荐服务 API 文档',
      });

      logger.log(`📖 Swagger文档: http://localhost:${configService.get<number>('PORT', 3070)}/${swaggerPath}`);
    }

    // 微服务配置
    const microservicePort = configService.get<number>('MICROSERVICE_PORT', 3071);
    const microserviceOptions: MicroserviceOptions = {
      transport: Transport.TCP,
      options: {
        host: '0.0.0.0',
        port: microservicePort,
      },
    };

    // 连接微服务
    app.connectMicroservice(microserviceOptions);
    await app.startAllMicroservices();
    logger.log(`📡 微服务已启动在端口: ${microservicePort}`);

    // 启动HTTP服务
    const port = configService.get<number>('PORT', 3070);
    const host = configService.get<string>('HOST', '0.0.0.0');

    await app.listen(port, host);

    logger.log(`🚀 智能推荐服务已启动`);
    logger.log(`📍 HTTP服务地址: http://${host}:${port}`);
    logger.log(`🌐 全局前缀: /${globalPrefix}`);
    logger.log(`📊 健康检查: http://${host}:${port}/${globalPrefix}/health`);
    logger.log(`🔧 环境: ${configService.get<string>('NODE_ENV', 'development')}`);

  } catch (error) {
    logger.error('服务启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭处理
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在优雅关闭...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在优雅关闭...');
  process.exit(0);
});

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason, 'at:', promise);
  process.exit(1);
});

bootstrap();
